// Global variables
let currentSection = 0;
let candlesBlown = 0;
const totalCandles = 3;

// Start the birthday journey
function startJourney() {
    const welcomeSection = document.getElementById('welcome');
    const cardSection = document.getElementById('cardSection');
    
    // Fade out welcome section
    welcomeSection.style.transition = 'opacity 1s ease-out';
    welcomeSection.style.opacity = '0';
    
    setTimeout(() => {
        welcomeSection.classList.add('hidden');
        cardSection.classList.remove('hidden');
        cardSection.style.opacity = '0';
        cardSection.style.transition = 'opacity 1s ease-in';
        
        setTimeout(() => {
            cardSection.style.opacity = '1';
        }, 100);
    }, 1000);
    
    currentSection = 1;
}

// Initialize card interactions
document.addEventListener('DOMContentLoaded', function() {
    const birthdayCard = document.getElementById('birthdayCard');
    const messageCards = document.querySelectorAll('.message-card');

    // Birthday card flip interaction
    if (birthdayCard) {
        birthdayCard.addEventListener('click', function() {
            this.classList.toggle('flipped');

            // After card is opened, show messages section
            if (this.classList.contains('flipped')) {
                setTimeout(() => {
                    showNextSection('messagesSection');
                }, 2000);
            }
        });
    }

    // Message cards flip interaction
    messageCards.forEach(card => {
        card.addEventListener('click', function() {
            this.classList.toggle('flipped');

            // Check if all message cards are flipped
            const flippedCards = document.querySelectorAll('.message-card.flipped');
            if (flippedCards.length === messageCards.length) {
                setTimeout(() => {
                    showNextSection('cakeSection');
                }, 1500);
            }
        });
    });


});

// Candle blowing function
function blowCandle(flame, candleNumber) {
    if (!flame.classList.contains('blown-out')) {
        flame.classList.add('blown-out');
        candlesBlown++;

        // Add blow effect
        createBlowEffect(flame);

        // Check if all candles are blown out
        if (candlesBlown === totalCandles) {
            setTimeout(() => {
                showCakeMessage();
            }, 500);

            setTimeout(() => {
                showWishMessage();
            }, 2000);

            setTimeout(() => {
                showNextSection('finalSection');
            }, 4000);
        }
    }
}

// Show birthday message on cake
function showCakeMessage() {
    const cakeHolder = document.getElementById('cake-holder');
    if (cakeHolder) {
        cakeHolder.classList.add('done');
    }

    // After showing cake message, automatically go to final section
    setTimeout(() => {
        showFinalSection();
    }, 3000);
}

// Show final section
function showFinalSection() {
    const currentSection = document.querySelector('.section:not(.hidden)');
    const finalSection = document.getElementById('finalSection');

    if (currentSection) {
        currentSection.classList.add('hidden');
    }

    if (finalSection) {
        finalSection.classList.remove('hidden');
    }
}

// Create blow effect for candles
function createBlowEffect(flame) {
    const rect = flame.getBoundingClientRect();
    const effect = document.createElement('div');
    effect.style.position = 'fixed';
    effect.style.left = rect.left + rect.width / 2 + 'px';
    effect.style.top = rect.top + 'px';
    effect.style.width = '25px';
    effect.style.height = '25px';
    effect.style.background = 'rgba(255, 255, 255, 0.9)';
    effect.style.borderRadius = '50%';
    effect.style.pointerEvents = 'none';
    effect.style.zIndex = '1000';
    effect.style.animation = 'blowEffect 1s ease-out forwards';

    document.body.appendChild(effect);

    setTimeout(() => {
        if (effect.parentNode) {
            document.body.removeChild(effect);
        }
    }, 1000);
}

// Add blow effect animation
const blowEffectStyle = document.createElement('style');
blowEffectStyle.textContent = `
    @keyframes blowEffect {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(5);
            opacity: 0;
        }
    }
`;
document.head.appendChild(blowEffectStyle);

// Show next section
function showNextSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.remove('hidden');
        section.style.opacity = '0';
        section.style.transition = 'opacity 1s ease-in';
        
        // Smooth scroll to section
        section.scrollIntoView({ behavior: 'smooth' });
        
        setTimeout(() => {
            section.style.opacity = '1';
        }, 100);
    }
}

// Show wish message
function showWishMessage() {
    const wishMessage = document.getElementById('wishMessage');
    if (wishMessage) {
        wishMessage.classList.remove('hidden');
        wishMessage.style.animation = 'fadeInUp 1s ease-out';
    }
}



// Celebration function
function celebrate() {
    createConfetti();
    playBirthdayMusic();
    showFinalMessage();
}

// Create confetti effect
function createConfetti() {
    const confettiContainer = document.getElementById('confettiContainer');
    const colors = ['#ff6b9d', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    
    for (let i = 0; i < 100; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 3 + 's';
        confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
        
        confettiContainer.appendChild(confetti);
        
        // Remove confetti after animation
        setTimeout(() => {
            if (confetti.parentNode) {
                confetti.parentNode.removeChild(confetti);
            }
        }, 5000);
    }
}

// Play birthday music (using Web Audio API)
function playBirthdayMusic() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const notes = [
            { freq: 261.63, duration: 0.5 }, // C
            { freq: 261.63, duration: 0.5 }, // C
            { freq: 293.66, duration: 1 },   // D
            { freq: 261.63, duration: 1 },   // C
            { freq: 349.23, duration: 1 },   // F
            { freq: 329.63, duration: 2 }    // E
        ];
        
        let currentTime = audioContext.currentTime;
        
        notes.forEach(note => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(note.freq, currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, currentTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + note.duration);
            
            oscillator.start(currentTime);
            oscillator.stop(currentTime + note.duration);
            
            currentTime += note.duration;
        });
    } catch (error) {
        console.log('Audio not supported');
    }
}

// Show final message
function showFinalMessage() {
    setTimeout(() => {
        alert('🎉 Happy Birthday, Vaishnavi! Thank you for being the most wonderful person in my life! 💕');
    }, 1000);
}

// Add floating hearts periodically
setInterval(() => {
    if (Math.random() < 0.3) {
        createFloatingHeart();
    }
}, 2000);

function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.innerHTML = '💖';
    heart.style.position = 'fixed';
    heart.style.left = Math.random() * window.innerWidth + 'px';
    heart.style.top = window.innerHeight + 'px';
    heart.style.fontSize = '2rem';
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = '1';
    heart.style.animation = 'floatUp 4s ease-out forwards';
    
    document.body.appendChild(heart);
    
    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 4000);
}

// Add float up animation
const floatUpStyle = document.createElement('style');
floatUpStyle.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(floatUpStyle);

// Add smooth scrolling behavior
document.documentElement.style.scrollBehavior = 'smooth';

// Add touch support for mobile devices
document.addEventListener('touchstart', function() {}, { passive: true });

// Initialize flowers animation
document.addEventListener('DOMContentLoaded', function() {
    // Start flowers animations immediately
    setTimeout(() => {
        document.body.classList.remove('not-loaded');
    }, 1000);
});
